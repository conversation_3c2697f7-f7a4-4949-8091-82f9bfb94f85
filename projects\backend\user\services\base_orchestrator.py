"""
Base Orchestrator Service

This service provides common functionality for all orchestrator services including:
- Request data extraction and logging
- Error handling patterns
- Request ID generation
- Common utility methods
"""

import logging
from typing import Dict, Any, Optional
import uuid

from agritram.exceptions import (
    ValidationException,
    DuplicateResourceException,
    AuthenticationException,
    DeviceVerificationRequiredException,
)

logger = logging.getLogger(__name__)


class BaseOrchestrator:
    """Base class for all orchestrator services with common functionality"""

    def __init__(self):
        """Initialize the base orchestrator with shared utilities"""
        # Use class methods, no need to instantiate
        pass

    def _generate_request_id(self) -> str:
        """Generate a unique request ID for tracking"""
        return str(uuid.uuid4())

    def _extract_request_data_safely(self, request) -> Dict[str, Any]:
        """Delegate to RequestUtilsService"""
        return RequestUtilsService.extract_request_data(request)

    def _extract_safe_headers(self, request) -> Dict[str, str]:
        """Delegate to LoggingUtilsService"""
        return LoggingUtilsService.extract_safe_headers(request)

    def _create_log_data(
        self,
        request_data: Dict[str, Any],
        headers: Dict[str, str],
        email: Optional[str] = None,
        sensitive_fields: list = None,
    ) -> str:
        """Delegate to LoggingUtilsService"""
        return LoggingUtilsService.create_single_line_json(
            request_data, headers, email, sensitive_fields
        )

    def _log_request_start(
        self,
        operation: str,
        request,
        request_id: str,
        additional_data: Dict[str, Any] = None,
    ) -> None:
        """Delegate to LoggingUtilsService"""
        LoggingUtilsService.log_operation_start(
            operation, request, request_id, additional_data
        )

    def _log_operation_step(
        self,
        operation: str,
        step: str,
        request_id: str,
        user_id: Optional[int] = None,
        **kwargs,
    ) -> None:
        """Delegate to LoggingUtilsService"""
        LoggingUtilsService.log_operation_step(
            operation, step, request_id, user_id, **kwargs
        )

    def _get_request_context(self, request) -> Dict[str, Any]:
        """Delegate to RequestUtilsService"""
        return RequestUtilsService.get_request_context(request)

    def _handle_exceptions(
        self, e: Exception, operation: str, request_id: str = None
    ) -> None:
        """
        Handle exceptions with standardized logging

        Args:
            e: Exception to handle
            operation: Operation name
            request_id: Request ID (optional)
        """
        if isinstance(
            e,
            (
                ValidationException,
                DuplicateResourceException,
                AuthenticationException,
                DeviceVerificationRequiredException,
            ),
        ):
            # Re-raise our custom exceptions to be handled by the global exception handler
            raise
        else:
            # Log unexpected errors
            logger.error(
                f"Unexpected error during {operation}",
                extra={
                    "operation": f"{operation.upper()}_UNEXPECTED_ERROR",
                    "request_id": request_id,
                    "error": str(e),
                    "error_type": type(e).__name__,
                },
                exc_info=True,
            )
            raise
