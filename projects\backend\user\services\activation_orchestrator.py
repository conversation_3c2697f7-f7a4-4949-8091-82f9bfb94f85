"""
Activation Orchestrator Service

This service coordinates all activation-related services to handle the complete
user activation flow in a structured and maintainable way.
"""

import logging
from typing import Dict, Any, Optional
from django.utils import timezone
from oauth2_auth.utils import get_client_ip
from user.models import User
from .base_orchestrator import BaseOrchestrator
from .activation_validation_service import ActivationValidationService
from .device_management_service import DeviceManagementService
from .activation_service import ActivationService

logger = logging.getLogger(__name__)


class ActivationOrchestrator(BaseOrchestrator):
    """Orchestrator for coordinating the complete user activation process"""

    def __init__(self):
        """Initialize the orchestrator with required services"""
        super().__init__()
        self.validation_service = ActivationValidationService()
        self.device_service = DeviceManagementService()
        self.activation_service = ActivationService()

    def activate_user_account(
        self, request, uid: Optional[str] = None, token: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Orchestrate the complete user activation process

        Args:
            request: HTTP request object
            uid: User ID from URL (optional)
            token: Activation token from URL (optional)

        Returns:
            Dict containing activation response data

        Raises:
            Various exceptions based on validation failures
        """
        # Generate unique request ID for this activation attempt
        request_id = str(uuid.uuid4())
        client_ip = get_client_ip(request)
        user_agent = request.META.get("HTTP_USER_AGENT", "")

        # Initialize variables for exception handling
        user = None

        try:
            # Log initial request using shared utilities
            self._log_request_start("ACTIVATION", request, request_id)

            # Step 1: Validate activation parameters and extract user
            logger.info(
                "Starting activation parameter validation",
                extra={
                    "operation": "ACTIVATION_VALIDATION_START",
                    "request_id": request_id,
                },
            )

            validation_result = self.validation_service.validate_activation_request(
                request, uid, token, request_id
            )

            user = validation_result["user"]
            uidb64 = validation_result["uidb64"]
            validated_token = validation_result["token"]
            device_id = validation_result["device_id"]

            logger.info(
                f"Activation validation completed for {user.email}",
                extra={
                    "operation": "ACTIVATION_VALIDATION_COMPLETE",
                    "request_id": request_id,
                    "user_id": user.id,
                    "has_device_id": bool(device_id),
                },
            )

            # Step 2: Check if user is already active
            if user.is_mail_verified:
                logger.warning(
                    f"OPERATION_WARNING: ACTIVATION_ALREADY_ACTIVE | message=Duplicate activation attempt for already active user | request_id={request_id} | user_id={user.id} | email={user.email[:3]}***@{user.email.split('@')[1] if '@' in user.email else '***'} | ip={client_ip} | user_agent={user_agent[:100]}"
                )
                return ResponseUtilsService.create_already_activated_response()

            # Step 3: Perform activation
            logger.info(
                "Starting user activation",
                extra={
                    "operation": "USER_ACTIVATION_START",
                    "request_id": request_id,
                    "user_id": user.id,
                },
            )

            activation_result = self.activation_service.activate_user_account(
                user, validated_token, device_id, request, request_id
            )

            logger.info(
                f"User activation completed for {user.email}",
                extra={
                    "operation": "USER_ACTIVATION_COMPLETE",
                    "request_id": request_id,
                    "user_id": user.id,
                    "device_registered": activation_result["device_registered"],
                    "welcome_email_sent": activation_result["welcome_email_sent"],
                },
            )

            # Step 4: Log comprehensive activation event
            self._log_activation_event(
                user, device_id, activation_result, request_id, client_ip, user_agent
            )

            # Log successful activation response
            logger.debug(
                f"OPERATION_DEBUG: ACTIVATION_RESPONSE_SUCCESS | message=Account activation response prepared | request_id={request_id} | user_id={user.id} | email={user.email[:3]}***@{user.email.split('@')[1] if '@' in user.email else '***'} | device_registered={activation_result['device_registered']}"
            )

            # Create response using shared utilities
            return ResponseUtilsService.create_activation_success_response(
                device_id=device_id,
                device_registered=activation_result["device_registered"],
                welcome_email_sent=activation_result["welcome_email_sent"],
            )

        except Exception as e:
            # Use base class exception handling
            self._handle_exceptions(e, "activation", request_id)

    def _log_activation_event(
        self,
        user: User,
        device_id: Optional[str],
        activation_result: Dict[str, Any],
        request_id: str,
        client_ip: str,
        user_agent: str,
    ) -> None:
        """Log comprehensive activation event"""
        activation_time = timezone.now()

        logger.info(
            f"OPERATION_INFO: ACTIVATION_SUCCESS | message=User account successfully activated | request_id={request_id} | user_id={user.id} | email={user.email[:3]}***@{user.email.split('@')[1] if '@' in user.email else '***'} | activation_time={activation_time.isoformat()} | device_registered={activation_result['device_registered']} | welcome_email_sent={activation_result['welcome_email_sent']} | ip={client_ip} | user_agent={user_agent[:100]}"
        )
