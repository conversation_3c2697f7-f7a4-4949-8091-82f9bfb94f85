"""
Activation Orchestrator Service

This service coordinates all activation-related services to handle the complete
user activation flow in a structured and maintainable way.
"""

import copy
import logging
from typing import Dict, Any, Optional, Tuple
import json
import uuid

from django.utils.encoding import force_str
from django.utils.http import urlsafe_base64_decode
from django.utils import timezone
from oauth2_auth.utils import get_client_ip, get_dynamic_device_info
from agritram.message_utils import StandardSuccessResponse
from agritram.exceptions import ValidationException, DuplicateResourceException, AuthenticationException, DeviceVerificationRequiredException
from user.models import User
from .activation_validation_service import ActivationValidationService
from .device_management_service import DeviceManagementService
from .activation_service import ActivationService

logger = logging.getLogger(__name__)


class ActivationOrchestrator:
    """Orchestrator for coordinating the complete user activation process"""

    def __init__(self):
        """Initialize the orchestrator with required services"""
        self.validation_service = ActivationValidationService()
        self.device_service = DeviceManagementService()
        self.activation_service = ActivationService()

    def _extract_request_data_safely(self, request) -> Dict[str, Any]:
        """Safely extract request data for logging"""
        try:
            if hasattr(request, 'data') and request.data:
                return dict(request.data)
            elif request.method == 'GET':
                return dict(request.GET)
            else:
                return {}
        except Exception:
            return {}

    def _extract_safe_headers(self, request) -> Dict[str, str]:
        """Extract safe headers for logging"""
        safe_headers = {}
        safe_header_keys = [
            'HTTP_USER_AGENT', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP',
            'HTTP_ACCEPT', 'HTTP_ACCEPT_LANGUAGE', 'HTTP_X_DEVICE_ID'
        ]
        
        for key in safe_header_keys:
            if key in request.META:
                safe_headers[key] = request.META[key]
        return safe_headers

    def _create_activation_log_data(self, request_data: Dict[str, Any], headers: Dict[str, str], email: Optional[str] = None) -> str:
        """Create single-line JSON log data for activation requests"""
        # Safely copy and mask sensitive data
        safe_data = copy.deepcopy(request_data)
        
        # Mask sensitive fields
        if 'token' in safe_data:
            safe_data['token'] = '***MASKED***'
        if 'uid' in safe_data:
            safe_data['uid'] = '***MASKED***'
        
        # Mask email if provided
        if email:
            safe_data['email'] = f"{email[:3]}***@{email.split('@')[1] if '@' in email else '***'}"

        # Create safe headers copy
        safe_headers = copy.deepcopy(headers)
        if 'HTTP_X_DEVICE_ID' in safe_headers:
            device_id = safe_headers['HTTP_X_DEVICE_ID']
            if len(device_id) > 8:
                safe_headers['HTTP_X_DEVICE_ID'] = f"{device_id[:4]}***{device_id[-4:]}"

        # Combine data and headers
        log_data = {"request_data": safe_data, "headers": safe_headers}

        # Return as single-line JSON
        return json.dumps(log_data, separators=(",", ":"))

    def activate_user_account(
        self, request, uid: Optional[str] = None, token: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Orchestrate the complete user activation process

        Args:
            request: HTTP request object
            uid: User ID from URL (optional)
            token: Activation token from URL (optional)

        Returns:
            Dict containing activation response data

        Raises:
            Various exceptions based on validation failures
        """
        # Generate unique request ID for this activation attempt
        request_id = str(uuid.uuid4())
        client_ip = get_client_ip(request)
        user_agent = request.META.get("HTTP_USER_AGENT", "")

        # Initialize variables for exception handling
        user = None
        
        try:
            # Log initial request
            try:
                request_data = self._extract_request_data_safely(request)
                headers = self._extract_safe_headers(request)

                logger.info(
                    f"OPERATION_INFO: ACTIVATION_REQUEST_START | message=Account activation request received | request_id={request_id} | method={request.method} | ip={client_ip} | data={self._create_activation_log_data(request_data, headers)}"
                )
            except Exception as e:
                # Fallback logging if data extraction fails
                logger.info(
                    f"OPERATION_INFO: ACTIVATION_REQUEST_START | message=Account activation request received | request_id={request_id} | method={request.method} | ip={client_ip} | data_extraction_error={str(e)}"
                )

            # Step 1: Validate activation parameters and extract user
            logger.info(
                "Starting activation parameter validation",
                extra={
                    "operation": "ACTIVATION_VALIDATION_START",
                    "request_id": request_id,
                },
            )

            validation_result = self.validation_service.validate_activation_request(
                request, uid, token, request_id
            )
            
            user = validation_result["user"]
            uidb64 = validation_result["uidb64"]
            validated_token = validation_result["token"]
            device_id = validation_result["device_id"]

            logger.info(
                f"Activation validation completed for {user.email}",
                extra={
                    "operation": "ACTIVATION_VALIDATION_COMPLETE",
                    "request_id": request_id,
                    "user_id": user.id,
                    "has_device_id": bool(device_id),
                },
            )

            # Step 2: Check if user is already active
            if user.is_mail_verified:
                logger.warning(
                    f"OPERATION_WARNING: ACTIVATION_ALREADY_ACTIVE | message=Duplicate activation attempt for already active user | request_id={request_id} | user_id={user.id} | email={user.email[:3]}***@{user.email.split('@')[1] if '@' in user.email else '***'} | ip={client_ip} | user_agent={user_agent[:100]}"
                )
                return StandardSuccessResponse.create_success_response(
                    code=StandardSuccessResponse.ACCOUNT_ACTIVATED,
                    message="Account is already activated",
                    details="Your account was previously activated and is ready to use",
                    actions="You can now log in to your account",
                )

            # Step 3: Perform activation
            logger.info(
                "Starting user activation",
                extra={
                    "operation": "USER_ACTIVATION_START",
                    "request_id": request_id,
                    "user_id": user.id,
                },
            )

            activation_result = self.activation_service.activate_user_account(
                user, validated_token, device_id, request, request_id
            )

            logger.info(
                f"User activation completed for {user.email}",
                extra={
                    "operation": "USER_ACTIVATION_COMPLETE",
                    "request_id": request_id,
                    "user_id": user.id,
                    "device_registered": activation_result["device_registered"],
                    "welcome_email_sent": activation_result["welcome_email_sent"],
                },
            )

            # Step 4: Log comprehensive activation event
            self._log_activation_event(
                user, device_id, activation_result, request_id, client_ip, user_agent
            )

            # Prepare response data in the required format
            response_data = {"device": {"id": device_id}}

            # Log successful activation response
            logger.debug(
                f"OPERATION_DEBUG: ACTIVATION_RESPONSE_SUCCESS | message=Account activation response prepared | request_id={request_id} | user_id={user.id} | email={user.email[:3]}***@{user.email.split('@')[1] if '@' in user.email else '***'} | device_registered={activation_result['device_registered']}"
            )

            return StandardSuccessResponse.create_success_response(
                code=StandardSuccessResponse.ACCOUNT_ACTIVATED,
                message="Account activated successfully",
                details="Your account has been activated and is now ready to use",
                actions="You can now log in to access all features",
                data=response_data,
            )

        except (
            ValidationException,
            DuplicateResourceException,
            AuthenticationException,
            DeviceVerificationRequiredException,
        ):
            # Re-raise our custom exceptions to be handled by the global exception handler
            raise
        except Exception as e:
            # Handle any unexpected errors that weren't caught by the activation logic
            logger.error(
                f"Unexpected error during activation",
                extra={
                    "operation": "ACTIVATION_UNEXPECTED_ERROR",
                    "request_id": request_id,
                    "user_id": user.id if user else None,
                    "error": str(e),
                    "error_type": type(e).__name__,
                },
                exc_info=True,
            )
            raise

    def _log_activation_event(
        self, user: User, device_id: Optional[str], activation_result: Dict[str, Any], 
        request_id: str, client_ip: str, user_agent: str
    ) -> None:
        """Log comprehensive activation event"""
        activation_time = timezone.now()
        
        logger.info(
            f"OPERATION_INFO: ACTIVATION_SUCCESS | message=User account successfully activated | request_id={request_id} | user_id={user.id} | email={user.email[:3]}***@{user.email.split('@')[1] if '@' in user.email else '***'} | activation_time={activation_time.isoformat()} | device_registered={activation_result['device_registered']} | welcome_email_sent={activation_result['welcome_email_sent']} | ip={client_ip} | user_agent={user_agent[:100]}"
        )
