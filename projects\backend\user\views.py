import logging
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.utils.encoding import force_bytes, force_str
from django.utils.http import urlsafe_base64_decode, urlsafe_base64_encode
from rest_framework.authentication import SessionAuthentication, TokenAuthentication
from rest_framework.authtoken.models import Token
from rest_framework.decorators import (
    api_view,
    authentication_classes,
    permission_classes,
)
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
import hashlib

logger = logging.getLogger(__name__)

from oauth2_auth.permissions import ProfilePermission
from oauth2_auth.config import oauth2_security_config
from agritram.message_utils import (
    StandardErrorResponse,
    StandardSuccessResponse,
    handle_serializer_errors,
    handle_exception_with_logging,
    get_frontend_url_by_role,
)
from agritram.exceptions import (
    ValidationException,
    AuthenticationException,
    DuplicateResourceException,
    DeviceVerificationRequiredException,
    raise_validation_error,
    raise_authentication_error,
    raise_not_found_error,
)

from .models import User
from .serializers import UserSerializer
from .services.registration_orchestrator import RegistrationOrchestrator


@api_view(["POST"])
@permission_classes([AllowAny])
def register(request):
    """
    Enhanced registration with OAuth2 integration, security logging, and email verification.

    This endpoint has been refactored to use a service-based architecture for better
    maintainability and testability. The registration flow is now handled by the
    RegistrationOrchestrator which coordinates multiple specialized services.

    Query Parameters:
        debug (bool): Include detailed response data for debugging (default: False)
        extended (bool): Include extended response data (default: False)
    """
    try:
        # Check for debug/extended response parameters
        debug_mode = request.GET.get("debug", "").lower() in ("true", "1", "yes")
        extended_mode = request.GET.get("extended", "").lower() in ("true", "1", "yes")

        # Use the registration orchestrator to handle the complete flow
        orchestrator = RegistrationOrchestrator()
        return orchestrator.register_user(
            request, debug_mode=debug_mode, extended_mode=extended_mode
        )

    except (
        ValidationException,
        DuplicateResourceException,
        AuthenticationException,
        DeviceVerificationRequiredException,
    ):
        # Re-raise our custom exceptions to be handled by the global exception handler
        raise
    except Exception as e:
        # Handle any unexpected errors that weren't caught by the orchestrator
        return handle_exception_with_logging(e, "user registration")


# Helper functions for activation account
def _make_json_serializable(obj):
    """Convert objects to JSON serializable format"""
    if isinstance(obj, dict):
        return {k: _make_json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [_make_json_serializable(item) for item in obj]
    elif hasattr(obj, "__dict__"):
        return str(obj)  # Convert objects to string representation
    elif isinstance(obj, (str, int, float, bool)) or obj is None:
        return obj
    else:
        return str(obj)  # Fallback to string representation


def _create_activation_log_data(request_data, headers, email=None):
    """Create single-line JSON log data for activation requests"""
    import json

    # Safely copy and mask sensitive data
    safe_data = {}
    if request_data:
        try:
            # Convert request_data to dict if it's not already
            if hasattr(request_data, "dict"):
                data_dict = request_data.dict()
            elif hasattr(request_data, "items"):
                data_dict = dict(request_data.items())
            else:
                data_dict = dict(request_data) if request_data else {}

            safe_data = {
                k: _make_json_serializable(v)
                for k, v in data_dict.items()
                if k not in ["password", "token"]
            }
            if "token" in data_dict:
                safe_data["token"] = "***MASKED***"
        except Exception:
            safe_data = {"error": "Unable to serialize request data"}

    # Mask email for privacy
    if email:
        safe_data["email"] = (
            email[:3] + "***@" + email.split("@")[1] if "@" in email else "***MASKED***"
        )

    # Safe headers (exclude sensitive ones and non-serializable objects)
    safe_headers = {}
    if headers:
        try:
            for k, v in headers.items():
                if k.upper() not in ["AUTHORIZATION", "COOKIE", "X-API-KEY"]:
                    safe_headers[k] = _make_json_serializable(v)
        except Exception:
            safe_headers = {"error": "Unable to serialize headers"}

    # Combine data and headers
    log_data = {"request_data": safe_data, "headers": safe_headers}

    # Return as single-line JSON
    try:
        return json.dumps(log_data, separators=(",", ":"))
    except Exception as e:
        # Fallback if JSON serialization still fails
        return f'{{"error": "JSON serialization failed: {str(e)}"}}'


def _extract_request_data_safely(request):
    """Safely extract request data from different request types"""
    try:
        if hasattr(request, "data") and request.data:
            return request.data
        elif hasattr(request, "POST") and request.POST:
            return request.POST
        elif hasattr(request, "GET") and request.GET:
            return request.GET
        else:
            return {}
    except Exception:
        return {}


def _extract_safe_headers(request):
    """Extract safe headers for logging"""
    headers = {}
    if hasattr(request, "META"):
        safe_header_keys = [
            "HTTP_USER_AGENT",
            "HTTP_ACCEPT",
            "HTTP_ACCEPT_LANGUAGE",
            "HTTP_ACCEPT_ENCODING",
            "CONTENT_TYPE",
            "CONTENT_LENGTH",
            "REQUEST_METHOD",
            "PATH_INFO",
            "QUERY_STRING",
        ]
        for key in safe_header_keys:
            if key in request.META:
                headers[key] = request.META[key]
    return headers


def _extract_activation_parameters(request, uid, token):
    """Extract activation parameters from request"""
    if request.method == "GET":
        # Handle URL parameters (from email links)
        return uid, token, request.GET.get("device_id")
    else:
        # Handle POST data (from frontend forms)
        try:
            request_data = _extract_request_data_safely(request)
            return (
                request_data.get("uid"),
                request_data.get("token"),
                request_data.get("device_id"),
            )
        except Exception:
            return None, None, None


def _handle_device_id_from_header(request, device_id, request_id):
    """Handle device ID extraction from X-Device-ID header"""
    header_device_id = request.META.get("HTTP_X_DEVICE_ID", "").strip()
    if header_device_id:
        logger.debug(
            f"OPERATION_DEBUG: ACTIVATION_DEVICE_ID_FROM_HEADER | message=Device ID extracted from X-Device-ID header | request_id={request_id} | device_id={header_device_id}"
        )
        return header_device_id
    elif not device_id:
        # Generate device_id if not provided (following registration pattern)
        from user.services.registration_validation_service import (
            RegistrationValidationService,
        )

        generated_device_id = RegistrationValidationService._generate_secure_device_id()
        logger.debug(
            f"OPERATION_DEBUG: ACTIVATION_DEVICE_ID_GENERATED | message=Device ID generated for activation | request_id={request_id} | device_id={generated_device_id}"
        )
        return generated_device_id
    return device_id


@api_view(["GET", "POST"])
@permission_classes([AllowAny])
def activate_account(request, uid=None, token=None):
    """
    Enhanced account activation with security logging and device validation

    Supports both GET (email links) and POST (frontend forms) requests.
    Includes comprehensive logging, device validation, and async email delivery.
    """
    try:
        # Import required services
        from oauth2_auth.utils import get_client_ip, get_dynamic_device_info
        from oauth2_auth.authentication import DeviceAuthenticationService
        from oauth2_auth.device_validation_service import device_validation_service
        from oauth2_auth.secure_token_service import secure_token_service
        from user.async_email_service import async_email_service
        import uuid
        import json

        # Initialize request tracking
        request_id = str(uuid.uuid4())
        client_ip = get_client_ip(request)
        user_agent = request.META.get("HTTP_USER_AGENT", "")

        # Log initial request
        try:
            request_data = _extract_request_data_safely(request)
            headers = _extract_safe_headers(request)

            logger.info(
                f"OPERATION_INFO: ACTIVATION_REQUEST_START | message=Account activation request received | request_id={request_id} | method={request.method} | ip={client_ip} | data={_create_activation_log_data(request_data, headers)}"
            )
        except Exception as e:
            # Fallback logging if data extraction fails
            logger.info(
                f"OPERATION_INFO: ACTIVATION_REQUEST_START | message=Account activation request received | request_id={request_id} | method={request.method} | ip={client_ip} | data_extraction_error={str(e)}"
            )

        # Extract activation parameters
        uidb64, token, device_id = _extract_activation_parameters(request, uid, token)

        # Handle device ID from X-Device-ID header or generate if missing
        device_id = _handle_device_id_from_header(request, device_id, request_id)

        # Get dynamic device information

        device_info = get_dynamic_device_info(request, "activation")
        device_name = device_info["device_name"]
        device_type = device_info["device_type"]

        # Log parameter extraction
        logger.info(
            f"OPERATION_INFO: ACTIVATION_PARAMS_EXTRACTED | message=Activation parameters extracted | request_id={request_id} | method={request.method} | has_uid={bool(uidb64)} | has_token={bool(token)} | has_device_id={bool(device_id)} | device_name={device_name} | device_type={device_type}"
        )

        if not uidb64 or not token:
            missing_params = []
            if not uidb64:
                missing_params.append("uid")
            if not token:
                missing_params.append("token")

            logger.warning(
                f"OPERATION_WARNING: ACTIVATION_MISSING_PARAMS | message=Activation attempt with missing parameters | request_id={request_id} | missing_params={missing_params} | ip={client_ip} | user_agent={user_agent[:100]}"
            )
            raise_validation_error(
                message="Invalid activation link",
                details="Both UID and token are required for account activation",
            )

        try:
            uid = force_str(urlsafe_base64_decode(uidb64))
            user = User.objects.get(pk=uid)

            # Log successful user lookup
            logger.debug(
                f"OPERATION_DEBUG: ACTIVATION_USER_FOUND | message=User found for activation | request_id={request_id} | user_id={user.id} | email={user.email[:3]}***@{user.email.split('@')[1] if '@' in user.email else '***'}"
            )

        except (TypeError, ValueError, OverflowError, User.DoesNotExist) as e:
            logger.warning(
                f"OPERATION_WARNING: ACTIVATION_INVALID_UID | message=Activation attempt with invalid UID | request_id={request_id} | uid={uidb64} | error_type={type(e).__name__} | ip={client_ip} | user_agent={user_agent[:100]}"
            )
            raise_validation_error(
                message="Invalid activation link",
                details="The activation link is malformed or the user does not exist",
            )

        # Check if user is already active
        if user.is_mail_verified:
            logger.warning(
                f"OPERATION_WARNING: ACTIVATION_ALREADY_ACTIVE | message=Duplicate activation attempt for already active user | request_id={request_id} | user_id={user.id} | email={user.email[:3]}***@{user.email.split('@')[1] if '@' in user.email else '***'} | ip={client_ip} | user_agent={user_agent[:100]}"
            )
            return StandardSuccessResponse.create_success_response(
                code=StandardSuccessResponse.ACCOUNT_ACTIVATED,
                message="Account is already activated",
                details="Your account was previously activated and is ready to use",
                actions="You can now log in to your account",
            )

        # Verify activation token using secure token service

        logger.debug(
            f"OPERATION_DEBUG: ACTIVATION_TOKEN_VALIDATION_START | message=Starting token validation | request_id={request_id} | user_id={user.id}"
        )

        is_valid, token_obj, error_message = secure_token_service.validate_token(
            raw_token=token, token_type="activation", request=request, user=user
        )

        logger.debug(
            f"OPERATION_DEBUG: ACTIVATION_TOKEN_VALIDATION_RESULT | message=Token validation completed | request_id={request_id} | user_id={user.id} | is_valid={is_valid} | error_message={error_message if not is_valid else 'None'}"
        )

        if is_valid and token_obj:
            # Validate device consistency for activation
            logger.debug(
                f"OPERATION_DEBUG: ACTIVATION_DEVICE_VALIDATION_START | message=Starting device validation | request_id={request_id} | user_id={user.id} | device_id={device_id}"
            )

            is_device_valid, device_message, device_details = (
                device_validation_service.validate_activation_device(
                    user=user, device_id=device_id, request=request
                )
            )

            logger.debug(
                f"OPERATION_DEBUG: ACTIVATION_DEVICE_VALIDATION_RESULT | message=Device validation completed | request_id={request_id} | user_id={user.id} | is_device_valid={is_device_valid} | security_score={device_details.get('security_score', 'N/A')} | device_message={device_message}"
            )

            # Log device validation result (but don't block activation unless security score is very low)
            if not is_device_valid and device_details.get("security_score", 100) < 30:
                logger.warning(
                    f"OPERATION_WARNING: ACTIVATION_DEVICE_VALIDATION_FAILED | message=Device validation failed during activation - blocking activation | request_id={request_id} | user_id={user.id} | device_id={device_id} | security_score={device_details.get('security_score')} | ip={client_ip} | user_agent={user_agent[:100]} | device_details={json.dumps(device_details, separators=(',', ':'))}"
                )
                raise_validation_error(
                    message="Device validation failed",
                    details="The device used for activation doesn't match the registration device",
                    error_code="DEVICE_VALIDATION_FAILED",
                )

            # Mark token as used
            logger.debug(
                f"OPERATION_DEBUG: ACTIVATION_TOKEN_USED | message=Marking token as used | request_id={request_id} | user_id={user.id} | token_id={token_obj.id if token_obj else 'N/A'}"
            )
            secure_token_service.use_token(token_obj, request)

            # Activate the user
            logger.debug(
                f"OPERATION_DEBUG: ACTIVATION_USER_ACTIVATION | message=Activating user account | request_id={request_id} | user_id={user.id}"
            )
            user.is_mail_verified = True
            user.save()

            # Register the activation device if provided
            if device_id:
                try:
                    logger.debug(
                        f"OPERATION_DEBUG: ACTIVATION_DEVICE_REGISTRATION_START | message=Starting device registration | request_id={request_id} | user_id={user.id} | device_id={device_id} | device_name={device_name} | device_type={device_type}"
                    )
                    DeviceAuthenticationService.register_device(
                        user, device_id, device_name, device_type, request
                    )
                    logger.debug(
                        f"OPERATION_DEBUG: ACTIVATION_DEVICE_REGISTRATION_SUCCESS | message=Device registration successful | request_id={request_id} | user_id={user.id} | device_id={device_id}"
                    )
                except Exception as e:
                    logger.warning(
                        f"OPERATION_WARNING: ACTIVATION_DEVICE_REGISTRATION_FAILED | message=Failed to register activation device | request_id={request_id} | user_id={user.id} | device_id={device_id} | error={str(e)} | error_type={type(e).__name__}"
                    )

            # Log successful activation
            activation_time = timezone.now()
            logger.info(
                f"OPERATION_INFO: ACTIVATION_SUCCESS | message=User account successfully activated | request_id={request_id} | user_id={user.id} | email={user.email[:3]}***@{user.email.split('@')[1] if '@' in user.email else '***'} | activation_time={activation_time.isoformat()} | device_registered={device_id is not None} | ip={client_ip} | user_agent={user_agent[:100]}"
            )

            # Send welcome email using async email service (Celery)
            from user.async_email_service import async_email_service

            logger.debug(
                f"OPERATION_DEBUG: ACTIVATION_WELCOME_EMAIL_START | message=Queueing welcome email via Celery | request_id={request_id} | user_id={user.id}"
            )

            try:
                # Queue welcome email asynchronously via Celery
                task_result = async_email_service.send_welcome_email_async(
                    user=user, ip_address=client_ip, user_agent=user_agent
                )

                logger.debug(
                    f"OPERATION_DEBUG: ACTIVATION_WELCOME_EMAIL_QUEUED | message=Welcome email queued successfully | request_id={request_id} | user_id={user.id} | task_id={task_result.id} | queue=email"
                )
            except Exception as e:
                logger.warning(
                    f"OPERATION_WARNING: ACTIVATION_WELCOME_EMAIL_QUEUE_FAILED | message=Failed to queue welcome email | request_id={request_id} | user_id={user.id} | error={str(e)} | error_type={type(e).__name__}"
                )

            # Prepare response data in the required format
            response_data = {"device": {"id": device_id}}

            # Log successful activation response
            logger.debug(
                f"OPERATION_DEBUG: ACTIVATION_RESPONSE_SUCCESS | message=Account activation response prepared | request_id={request_id} | user_id={user.id} | email={user.email[:3]}***@{user.email.split('@')[1] if '@' in user.email else '***'} | device_registered={device_id is not None}"
            )

            return StandardSuccessResponse.create_success_response(
                code=StandardSuccessResponse.ACCOUNT_ACTIVATED,
                message="Account activated successfully",
                details="Your account has been activated and is now ready to use",
                actions="You can now log in to access all features",
                data=response_data,
            )

        else:
            logger.warning(
                f"OPERATION_WARNING: ACTIVATION_INVALID_TOKEN | message=Activation attempt with invalid or expired token | request_id={request_id} | user_id={user.id} | email={user.email[:3]}***@{user.email.split('@')[1] if '@' in user.email else '***'} | error_message={error_message} | token_status={token_obj.status if token_obj else 'not_found'} | ip={client_ip} | user_agent={user_agent[:100]}"
            )
            raise_validation_error(
                message="Activation link is invalid or has expired",
                details=error_message,
            )

    except (
        ValidationException,
        DuplicateResourceException,
        AuthenticationException,
        DeviceVerificationRequiredException,
    ):
        # Re-raise our custom exceptions to be handled by the global exception handler
        raise
    except Exception as e:
        # Handle any unexpected errors that weren't caught by the activation logic
        return handle_exception_with_logging(e, "account activation")


@api_view(["POST"])
@permission_classes([AllowAny])
def resend_device_otp(request):
    """
    Resend OTP for device verification
    """
    try:
        from oauth2_auth.utils import generate_otp, send_otp_email, get_client_ip
        from oauth2_auth.config import oauth2_security_config
        from django.core.cache import cache

        # Log incoming resend OTP request
        logger.info(
            f"Resend OTP request: {request.method} {request.path}",
            extra={"operation": "RESEND_OTP_REQUEST"},
        )

        email = request.data.get("email")
        device_id = request.data.get("device_id")

        if not email or not device_id:
            raise_validation_error(
                message="Email and device ID are required",
                details="Both email and device_id must be provided",
            )

        # Get user
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            raise_not_found_error(
                message="User not found",
                details=f"No user found with email {email}",
                resource_type="user",
            )

        # Check rate limiting for OTP resend
        resend_key = f"otp_resend_{user.id}_{device_id}"
        last_resend = cache.get(resend_key)

        if last_resend:
            raise_validation_error(
                message="Please wait before requesting another code",
                details="You can only request a new code every 60 seconds",
            )

        # Generate new OTP
        otp = generate_otp()

        # Store OTP in cache
        otp_key = f"device_otp_{user.id}_{device_id}"
        cache.set(otp_key, otp, oauth2_security_config.OTP_EXPIRY_SECONDS)

        # Set resend rate limit
        cache.set(resend_key, True, 60)  # 60 seconds cooldown

        # Send OTP email
        client_ip = get_client_ip(request)

        # Get dynamic device information
        from oauth2_auth.utils import get_dynamic_device_info

        device_info = get_dynamic_device_info(request, "otp_resend")
        device_name = device_info["device_name"]

        send_otp_email(user, otp, device_name, client_ip)

        logger.info(
            f"OTP resent successfully to {email}",
            extra={
                "operation": "OTP_RESEND_SUCCESS",
                "email": email,
                "device_id": device_id,
            },
        )

        return Response(
            {
                "message": "Verification code sent successfully",
                "details": "A new verification code has been sent to your email",
            },
            status=200,
        )

    except (ValidationException, AuthenticationException) as e:
        # Log authentication exceptions
        logger.warning(
            "OTP resend failed due to authentication error",
            extra={"operation": "OTP_RESEND_ERROR", "error_type": type(e).__name__},
        )
        raise
    except Exception as e:
        # Log unexpected OTP resend error
        logger.error(
            f"Unexpected OTP resend error: {str(e)}",
            extra={"operation": "OTP_RESEND_ERROR", "error": str(e)},
            exc_info=True,
        )
        return handle_exception_with_logging(e, "resend device OTP")


@api_view(["POST"])
@permission_classes([AllowAny])
def login(request):
    """
    Enhanced login with OAuth2 integration, device detection, and email notifications
    """
    try:
        # Import OAuth2 utilities and security services
        from oauth2_auth.utils import (
            get_client_ip,
            generate_device_fingerprint,
            send_otp_email,
            detect_suspicious_activity,
        )

        # Log incoming login request
        logger.info(
            f"Login request: {request.method} {request.path}",
            extra={"operation": "LOGIN_REQUEST"},
        )
        from oauth2_auth.authentication import DeviceAuthenticationService
        from oauth2_auth.jwt_rotation_service import jwt_rotation_service
        from oauth2_auth.rate_limiting_service import rate_limiting_service
        from oauth2_provider.models import Application
        from django.core.cache import cache
        import secrets

        # Extract login data
        email = request.data.get("email")
        password = request.data.get("password")
        device_id = request.data.get("device_id")
        otp_code = request.data.get("otp_code")  # For device verification

        # Get dynamic device information
        from oauth2_auth.utils import get_dynamic_device_info

        device_info = get_dynamic_device_info(request, "login")
        device_name = device_info["device_name"]
        device_type = device_info["device_type"]

        # Validate required fields
        if not email or not password:
            raise_validation_error(
                message="Email and password are required",
                details="Both email and password fields must be provided for login",
            )

        # Generate secure device_id if not provided
        if not device_id:
            device_id = f"login_device_{secrets.token_urlsafe(32)}"

        # Validate device_id format and length for security
        if not device_id or len(device_id) < 10:
            raise_validation_error(
                message="Invalid device identifier",
                details="Device ID must be at least 10 characters long",
            )

        # Extract additional device information for enhanced security
        device_fingerprint = request.data.get("device_fingerprint")
        device_info = request.data.get("device_info", {})

        # Get client information
        client_ip = get_client_ip(request)
        user_agent = request.META.get("HTTP_USER_AGENT", "")
        fingerprint = generate_device_fingerprint(request)

        # Check rate limiting for login attempts
        rate_check_id = f"{client_ip}_login"
        is_allowed, rate_info = rate_limiting_service.check_rate_limit(
            rate_check_id, "login", request
        )
        if not is_allowed:
            raise_validation_error(
                message="Too many login attempts",
                details=rate_info.get("message", "Please try again later"),
                error_code="RATE_LIMITED",
            )

        try:
            user = User.objects.get(email=email)

            # Check if user account is deleted
            if user.is_deleted:
                logger.warning(
                    "Login attempt by deleted user",
                    extra={
                        "event_type": "deleted_user_login_attempt",
                        "description": "Login attempt by deleted user",
                        "ip_address": client_ip,
                        "user_agent": user_agent,
                        "metadata": {"email": email},
                    },
                )
                raise_authentication_error(
                    message="Account not found",
                    details="This account is no longer available",
                    error_code="ACCOUNT_DELETED",
                )

            # Check if user account is unverified
            if not user.is_mail_verified:
                logger.warning(
                    "Login attempt by unverified user",
                    extra={
                        "event_type": "unverified_user_login_attempt",
                        "description": "Login attempt by unverified user",
                        "ip_address": client_ip,
                        "user_agent": user_agent,
                        "metadata": {"email": email},
                    },
                )
                raise_authentication_error(
                    message="Account has not been verified",
                    details="Please verify your account by clicking the link in your email",
                    error_code="ACCOUNT_UNVERIFIED",
                )

            # Check if user is active
            if not user.is_active:
                logger.warning(
                    "Login attempt by inactive user",
                    extra={
                        "event_type": "inactive_user_login_attempt",
                        "description": "Login attempt by inactive user",
                        "ip_address": client_ip,
                        "user_agent": user_agent,
                        "metadata": {"email": email},
                    },
                )
                raise_authentication_error(
                    message="User account is inactive",
                    details="Please activate your account by clicking the link in your email",
                    error_code="ACCOUNT_INACTIVE",
                )

            # Check account lockout status
            from .account_lockout_service import AccountLockoutService

            lockout_status = AccountLockoutService.check_account_lockout(user)

            if lockout_status["is_locked"]:
                logger.warning(
                    "Login attempt on locked account",
                    extra={
                        "event_type": "locked_account_login_attempt",
                        "description": "Login attempt on locked account",
                        "ip_address": client_ip,
                        "user_agent": user_agent,
                        "metadata": {
                            "email": email,
                            "is_permanent": lockout_status["is_permanent"],
                            "remaining_time": lockout_status["remaining_time"],
                        },
                    },
                )

                if lockout_status["is_permanent"]:
                    raise_authentication_error(
                        message="Account permanently locked",
                        details="Your account has been permanently locked due to repeated failed login attempts. Please contact support to unlock your account.",
                        error_code="ACCOUNT_PERMANENTLY_LOCKED",
                    )
                else:
                    raise_authentication_error(
                        message="Account temporarily locked",
                        details=lockout_status["message"],
                        error_code="ACCOUNT_TEMPORARILY_LOCKED",
                        metadata={"remaining_time": lockout_status["remaining_time"]},
                    )

            # Verify password
            if not user.check_password(password):
                # Handle failed login attempt with lockout logic
                lockout_info = AccountLockoutService.handle_failed_login(user, request)

                # Check for suspicious activity
                detect_suspicious_activity(user, request, "failed_login")

                # Prepare error response based on lockout status
                if lockout_info["is_locked"]:
                    if lockout_info["is_permanent"]:
                        raise_authentication_error(
                            message="Account permanently locked",
                            details="Your account has been permanently locked due to repeated failed login attempts. Please contact support to unlock your account.",
                            error_code="ACCOUNT_PERMANENTLY_LOCKED",
                        )
                    else:
                        raise_authentication_error(
                            message="Account locked due to failed attempts",
                            details=lockout_info["message"],
                            error_code="ACCOUNT_TEMPORARILY_LOCKED",
                            metadata={
                                "lockout_duration": lockout_info["lockout_duration"]
                            },
                        )
                else:
                    raise_authentication_error(
                        message="Invalid password",
                        details=f"The password provided is incorrect. {lockout_info['message']}",
                        metadata={
                            "attempts_remaining": lockout_info["attempts_remaining"]
                        },
                    )

        except User.DoesNotExist:
            logger.warning(
                "Login attempt with non-existent email",
                extra={
                    "event_type": "FAILED_LOGIN_NONEXISTENT_USER",
                    "description": "Login attempt with non-existent email",
                    "ip_address": client_ip,
                    "user_agent": user_agent,
                    "metadata": {"email": email},
                },
            )
            raise_authentication_error(
                message="User does not exist",
                details="No account found with this email address",
            )

        # Enhanced device detection and verification
        # Device ID should already be validated above, but double-check
        if not device_id:
            device_id = f"device_{secrets.token_urlsafe(32)}"

        # Check if this is a new device with enhanced validation
        is_new_device = True
        device_security_score = 0

        try:
            device = user.device_tokens.get(device_id=device_id)
            is_new_device = False

            # Enhanced device fingerprint validation
            current_fingerprint = device_fingerprint or fingerprint
            stored_fingerprint = device.fingerprint

            # Check for fingerprint changes (potential security issue)
            if stored_fingerprint and current_fingerprint != stored_fingerprint:
                # Calculate fingerprint similarity score
                from oauth2_auth.utils import calculate_fingerprint_similarity

                similarity_score = calculate_fingerprint_similarity(
                    stored_fingerprint, current_fingerprint
                )

                logger.warning(
                    "Device fingerprint changed - potential security issue",
                    extra={
                        "event_type": "device_fingerprint_changed",
                        "description": "Device fingerprint changed - potential security issue",
                        "ip_address": client_ip,
                        "user_agent": user_agent,
                        "device_id": device_id,
                        "metadata": {
                            "old_fingerprint": stored_fingerprint,
                            "new_fingerprint": current_fingerprint,
                            "similarity_score": similarity_score,
                            "device_info": device_info,
                        },
                    },
                )

                # If fingerprint change is significant, treat as new device
                if similarity_score < 0.7:  # Less than 70% similarity
                    is_new_device = True
                    device_security_score = 30  # Medium risk
                else:
                    device_security_score = 70  # Low risk
            else:
                device_security_score = 90  # High trust

            # Validate IP address consistency
            if device.ip_address and device.ip_address != client_ip:
                logger.warning(
                    "Device IP address changed",
                    extra={
                        "event_type": "device_ip_changed",
                        "description": "Device IP address changed",
                        "ip_address": client_ip,
                        "user_agent": user_agent,
                        "device_id": device_id,
                        "metadata": {
                            "old_ip": device.ip_address,
                            "new_ip": client_ip,
                            "device_info": device_info,
                        },
                    },
                )
                # IP change is normal, but reduce security score slightly
                device_security_score = max(device_security_score - 10, 0)

        except user.device_tokens.model.DoesNotExist:
            is_new_device = True

        # CRITICAL SECURITY CHECK: Prevent authentication bypass
        # If this is a new device, OTP verification is MANDATORY
        if is_new_device and not otp_code:
            # This is a security-critical path - new device MUST be verified
            logger.warning(
                "Attempt to bypass device verification for new device",
                extra={
                    "event_type": "authentication_bypass_attempt",
                    "description": "Attempt to bypass device verification for new device",
                    "ip_address": client_ip,
                    "user_agent": user_agent,
                    "device_id": device_id,
                    "metadata": {
                        "device_name": device_name,
                        "fingerprint": device_fingerprint or fingerprint,
                        "security_score": device_security_score,
                    },
                },
            )

            # Generate and send OTP - this is mandatory for new devices
            # otp = generate_otp()
            otp_key = f"device_otp_{user.id}_{device_id}"
            cache.set(otp_key, otp, oauth2_security_config.OTP_EXPIRY_SECONDS)

            # Send OTP email
            send_otp_email(user, otp, device_name, client_ip)

            # Log the OTP generation
            logger.info(
                f"Mandatory OTP sent for new device: {device_name}",
                extra={
                    "event_type": "mandatory_device_verification",
                    "description": f"Mandatory OTP sent for new device: {device_name}",
                    "ip_address": client_ip,
                    "user_agent": user_agent,
                    "device_id": device_id,
                    "metadata": {
                        "device_name": device_name,
                        "fingerprint": device_fingerprint or fingerprint,
                    },
                },
            )

            # Return device verification required error - NO BYPASS ALLOWED
            raise DeviceVerificationRequiredException(
                message="Device verification required for security",
                details="This device is not recognized. Please check your email for verification code.",
                device_id=device_id,
            )

        # Handle new device verification
        if is_new_device:
            if not otp_code:
                # Generate and send OTP for new device
                otp = "123456"

                # Store OTP in cache for verification
                otp_key = f"device_otp_{user.id}_{device_id}"
                cache.set(otp_key, otp, oauth2_security_config.OTP_EXPIRY_SECONDS)

                # Send OTP email
                send_otp_email(user, otp, device_name, client_ip)

                logger.info(
                    f"New device detected, OTP sent: {device_name}",
                    extra={
                        "event_type": "new_device_detected",
                        "description": f"New device detected, OTP sent: {device_name}",
                        "ip_address": client_ip,
                        "user_agent": user_agent,
                        "device_id": device_id,
                        "metadata": {
                            "device_name": device_name,
                            "fingerprint": fingerprint,
                        },
                    },
                )

                raise DeviceVerificationRequiredException(
                    message="New device detected. Please check your email for verification code.",
                    details="A verification code has been sent to your registered email address",
                    device_id=device_id,
                )

            else:
                # Verify OTP for new device
                otp_key = f"device_otp_{user.id}_{device_id}"
                stored_otp = cache.get(otp_key)

                if not stored_otp or stored_otp != otp_code:
                    logger.warning(
                        "Invalid OTP provided for device verification",
                        extra={
                            "event_type": "invalid_otp",
                            "description": "Invalid OTP provided for device verification",
                            "ip_address": client_ip,
                            "user_agent": user_agent,
                            "device_id": device_id,
                        },
                    )
                    raise_validation_error(
                        message="Invalid or expired verification code",
                        details="The OTP code provided is incorrect or has expired",
                    )

                # OTP verified, register device
                DeviceAuthenticationService.register_device(
                    user, device_id, device_name, device_type, request
                )

                # Clear OTP from cache
                cache.delete(otp_key)

                logger.info(
                    f"Device verified and registered: {device_name}",
                    extra={
                        "event_type": "DEVICE_VERIFIED",
                        "description": f"Device verified and registered: {device_name}",
                        "user_id": user.id,
                        "request": request,
                        "metadata": {
                            "device_id": device_id,
                            "device_name": device_name,
                        },
                    },
                )

        # Create OAuth2 application and generate JWT token (enhanced security)
        try:
            # Get default OAuth2 application or create one
            application, created = Application.objects.get_or_create(
                name="Agritram Default App",
                defaults={
                    "client_type": Application.CLIENT_CONFIDENTIAL,
                    "authorization_grant_type": Application.GRANT_AUTHORIZATION_CODE,
                    "client_id": "agritram-default-client",
                    "client_secret": secrets.token_urlsafe(32),
                },
            )

            # Enhanced JWT token generation with device-IP binding
            from oauth2_auth.utils import validate_device_security

            # Calculate device security score
            device_security_score = validate_device_security(
                device_info, device_fingerprint or fingerprint, client_ip, user_agent
            )

            # Generate JWT token pair with enhanced security metadata
            token_pair = jwt_rotation_service.generate_token_pair(
                user=user,
                application=application,
                device_id=device_id,
                request=request,
                metadata={
                    "device_fingerprint": device_fingerprint or fingerprint,
                    "device_security_score": device_security_score,
                    "device_info": device_info,
                    "ip_address": client_ip,
                    "user_agent_hash": hashlib.sha256(user_agent.encode()).hexdigest()[
                        :16
                    ],
                },
            )

            if "error" in token_pair:
                raise Exception(
                    f"Failed to generate JWT token pair: {token_pair['error']}"
                )

            jwt_token = token_pair["access_token"]
            refresh_token = token_pair["refresh_token"]

            # Store device-token-IP mapping for validation
            from django.core.cache import cache

            device_token_key = f"device_token_{device_id}_{user.id}"
            cache.set(
                device_token_key,
                {
                    "token_hash": hashlib.sha256(jwt_token.encode()).hexdigest(),
                    "ip_address": client_ip,
                    "user_agent_hash": hashlib.sha256(user_agent.encode()).hexdigest()[
                        :16
                    ],
                    "device_fingerprint": device_fingerprint or fingerprint,
                    "created_at": timezone.now().isoformat(),
                    "security_score": device_security_score,
                },
                86400,
            )  # 24 hours

        except Exception as e:
            logger.error(
                f"Token creation error: {str(e)}",
                extra={"operation": "TOKEN_CREATION_ERROR", "error": str(e)},
                exc_info=True,
            )
            return StandardErrorResponse.server_error(
                message="Failed to create authentication token",
                details="Unable to generate JWT token for authentication",
            )

        # Log successful login with standardized logging
        logger.info(
            "Successful user login",
            extra={
                "operation": "USER_LOGIN_SUCCESS",
                "entity_type": "USER",
                "entity_id": str(user.id),
                "user_id": user.id,
                "user_email": user.email,
                "device_id": device_id,
                "device_name": device_name,
                "authentication_method": "password",
                "is_new_device": is_new_device,
                "device_security_score": device_security_score,
            },
        )

        # Send login notification email using email service
        from oauth2_auth.email_service import email_service

        email_service.send_login_notification(user, device_name, client_ip, user_agent)

        # Detect suspicious activity
        detect_suspicious_activity(user, request, "login")

        # Reset failed login attempts on successful login
        user.reset_failed_attempts()

        # Update user last login
        user.last_login = timezone.now()
        user.save(update_fields=["last_login"])

        # Get user's OAuth2 application for frontend
        user_oauth_app = None
        try:
            from oauth2_provider.models import Application

            user_oauth_app = Application.objects.filter(user=user).first()
        except Exception as e:
            logger.warning(
                f"Failed to retrieve OAuth2 application for user {user.email}",
                extra={
                    "operation": "OAUTH_APP_RETRIEVAL",
                    "user_email": user.email,
                    "error": str(e),
                },
            )

        # Prepare enhanced token data with refresh token
        token_data = {
            "access_token": jwt_token,
            "refresh_token": refresh_token,
            "token_type": "Bearer",
            "expires_in": token_pair.get(
                "expires_in", oauth2_security_config.ACCESS_TOKEN_LIFETIME
            ),
            "refresh_expires_in": token_pair.get(
                "refresh_expires_in", oauth2_security_config.REFRESH_TOKEN_LIFETIME
            ),
            "scope": "read write profile email",
            "device_id": device_id,
        }

        # Add OAuth2 credentials if available
        if user_oauth_app:
            token_data["oauth2_client_id"] = user_oauth_app.client_id
            token_data["oauth2_redirect_uri"] = user_oauth_app.redirect_uris

        # Log successful login response
        logger.info(
            "Login response",
            extra={
                "operation": "LOGIN_SUCCESS",
                "status_code": 200,
                "message": "Login successful",
                "user_id": user.id,
                "user_email": user.email,
                "device_id": device_id,
            },
        )

        return StandardSuccessResponse.login_success(
            message="Login successful",
            details="Authentication completed and session established",
            user_data=UserSerializer(user).data,
            token_data=token_data,
        )

    except (
        DuplicateResourceException,
        ValidationException,
        AuthenticationException,
        DeviceVerificationRequiredException,
    ) as e:
        # Log authentication exceptions
        logger.warning(
            "Login failed due to authentication error",
            extra={
                "operation": "LOGIN_AUTHENTICATION_ERROR",
                "error_type": type(e).__name__,
            },
        )
        # Re-raise our custom exceptions to be handled by the exception handler
        raise
    except Exception as e:
        # Log unexpected login error
        logger.error(
            f"Unexpected login error: {str(e)}",
            extra={"operation": "LOGIN_ERROR", "error": str(e)},
            exc_info=True,
        )
        return handle_exception_with_logging(e, "user login")


@api_view(["GET", "PUT"])
@permission_classes([ProfilePermission])
def update_user(request):
    """
    Update an existing user.
    """
    user = request.user

    if request.method == "GET":
        serializer = UserSerializer(user)
        return StandardSuccessResponse.data_retrieved(
            message="User profile retrieved successfully",
            details="Current user profile information",
            data=serializer.data,
        )

    if request.method == "PUT":
        if not request.data:
            raise_validation_error(
                message="No data provided to update",
                details="Request body must contain data to update the user profile",
            )

        serializer = UserSerializer(user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return StandardSuccessResponse.record_updated(
                message="User profile updated successfully",
                details="Profile changes have been saved",
                record_data=serializer.data,
            )
        return handle_serializer_errors(serializer)


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def reset_password(request):
    """
    Allow authenticated users to change their password.
    """
    user = request.user
    old_password = request.data.get("old_password")
    new_password = request.data.get("new_password")

    if not old_password or not new_password:
        raise_validation_error(
            message="Old and new passwords are required",
            details="Both old_password and new_password fields must be provided",
        )

    if not user.check_password(old_password):
        raise_authentication_error(
            message="Old password is incorrect",
            details="The current password provided does not match",
        )

    try:
        validate_password(new_password, user)
    except ValidationError as e:
        raise_validation_error(
            message="New password validation failed", details="; ".join(e.messages)
        )

    user.set_password(new_password)
    user.save()
    return StandardSuccessResponse.create_success_response(
        code=StandardSuccessResponse.PASSWORD_CHANGED,
        message="Password changed successfully",
        details="Your password has been updated and is now active",
        actions="You can now log in with your new password",
    )


@api_view(["POST"])
@permission_classes([AllowAny])
def forgot_password(request):
    """
    Handle forgotten passwords by sending a reset link.
    """

    # Log incoming password reset request
    logger.info(
        f"Password reset request: {request.method} {request.path}",
        extra={"operation": "PASSWORD_RESET_REQUEST"},
    )

    email = request.data.get("email")
    if not email:
        raise_validation_error(
            message="Email is required",
            details="Email address must be provided to reset password",
        )

    try:
        user = User.objects.get(email=email)
    except User.DoesNotExist:
        raise_validation_error(
            message="User with this email does not exist",
            details="No account found with the provided email address",
        )

    # Check if account is locked and prevent password reset during lockout
    from .account_lockout_service import AccountLockoutService

    lockout_status = AccountLockoutService.check_account_lockout(user)

    if lockout_status["is_locked"]:
        from oauth2_auth.utils import get_client_ip

        # Log password reset attempt on locked account
        client_ip = get_client_ip(request)
        user_agent = request.META.get("HTTP_USER_AGENT", "")

        logger.warning(
            "Password reset attempt on locked account",
            extra={
                "event_type": "password_reset_attempt_locked_account",
                "description": "Password reset attempt on locked account",
                "ip_address": client_ip,
                "user_agent": user_agent,
                "metadata": {
                    "email": email,
                    "is_permanent": lockout_status["is_permanent"],
                    "remaining_time": lockout_status["remaining_time"],
                },
            },
        )

        if lockout_status["is_permanent"]:
            raise_authentication_error(
                message="Password reset not available",
                details="Your account is permanently locked. Please contact support to unlock your account before resetting your password.",
                error_code="ACCOUNT_PERMANENTLY_LOCKED",
            )
        else:
            raise_authentication_error(
                message="Password reset temporarily unavailable",
                details=f"Password reset is not available while your account is locked. {lockout_status['message']}",
                error_code="ACCOUNT_TEMPORARILY_LOCKED",
                metadata={"remaining_time": lockout_status["remaining_time"]},
            )

    # Generate secure password reset token using new token service
    from oauth2_auth.secure_token_service import secure_token_service
    from oauth2_auth.utils import get_client_ip

    client_ip = get_client_ip(request)
    user_agent = request.META.get("HTTP_USER_AGENT", "")

    reset_token, token_obj = secure_token_service.generate_token(
        user=user,
        token_type="password_reset",
        request=request,
        metadata={
            "reset_request_ip": client_ip,
            "reset_request_user_agent": user_agent,
        },
    )
    uid = urlsafe_base64_encode(force_bytes(user.pk))

    # Construct password reset URL using role-specific frontend URL
    frontend_url = get_frontend_url_by_role(user.role)
    reset_url = f"{frontend_url}/reset-password/{uid}/{reset_token}/"

    # Send enhanced password reset email using email service
    from oauth2_auth.email_service import email_service

    try:
        email_service.send_password_reset(user, reset_url, client_ip, user_agent)

        # Log successful password reset email sent
        logger.info(
            f"Password reset email sent to {user.email}",
            extra={
                "operation": "PASSWORD_RESET_EMAIL_SENT",
                "user_email": user.email,
                "reset_url": reset_url,
            },
        )

    except Exception as e:
        logger.error(
            f"Failed to send password reset email to {user.email}",
            extra={
                "operation": "PASSWORD_RESET_EMAIL_ERROR",
                "user_email": user.email,
                "error": str(e),
            },
            exc_info=True,
        )
        return StandardErrorResponse.external_service_error(
            message="Failed to send email",
            details="Unable to send password reset email. Please try again later",
            service_name="email_service",
        )

    return StandardSuccessResponse.create_success_response(
        code=StandardSuccessResponse.EMAIL_SENT,
        message="Password reset link sent successfully",
        details="A password reset link has been sent to your email address",
        actions="Please check your email and follow the instructions to reset your password",
    )


@api_view(["POST"])
@permission_classes([AllowAny])
def reset_password_confirm(request):
    """
    Confirm the password reset by setting the new password.
    """

    # Log incoming password reset confirmation request
    logger.info(
        f"Password reset confirm request: {request.method} {request.path}",
        extra={"operation": "PASSWORD_RESET_CONFIRM_REQUEST"},
    )

    uidb64 = request.data.get("uid")
    token = request.data.get("token")
    new_password = request.data.get("password")

    if not uidb64 or not token or not new_password:
        raise_validation_error(
            message="Invalid data",
            details="UID, token, and new password are all required",
        )

    try:
        uid = force_str(urlsafe_base64_decode(uidb64))
        user = User.objects.get(pk=uid)
    except (TypeError, ValueError, OverflowError, User.DoesNotExist):
        user = None

    # Verify password reset token using secure token service
    from oauth2_auth.secure_token_service import secure_token_service

    is_valid, token_obj, error_message = secure_token_service.validate_token(
        raw_token=token, token_type="password_reset", request=request, user=user
    )

    if is_valid and token_obj and user is not None:
        try:
            validate_password(new_password, user)
        except ValidationError as e:
            raise_validation_error(
                message="Password validation failed", details="; ".join(e.messages)
            )

        # Mark token as used
        secure_token_service.use_token(token_obj, request)

        user.set_password(new_password)
        user.save()

        logger.info(
            "Password reset completed successfully",
            extra={
                "event_type": "PASSWORD_RESET_COMPLETED",
                "description": "Password reset completed successfully",
                "user_id": user.id,
                "request": request,
                "metadata": {
                    "token_id": str(token_obj.id),
                    "reset_time": timezone.now().isoformat(),
                },
            },
        )

        return StandardSuccessResponse.create_success_response(
            code=StandardSuccessResponse.PASSWORD_RESET_SUCCESS,
            message="Password reset successfully",
            details="Your password has been reset and is now active",
            actions="You can now log in with your new password",
        )
    else:

        logger.warning(
            "Invalid password reset token",
            extra={
                "event_type": "INVALID_PASSWORD_RESET_TOKEN",
                "description": "Password reset attempt with invalid or expired token",
                "user_id": user.id if user else "N/A",
                "request": request,
                "metadata": {
                    "error": error_message,
                    "token_status": token_obj.status if token_obj else "not_found",
                },
            },
        )

        raise_validation_error(
            message="Invalid token or user ID",
            details=(
                error_message
                if error_message
                else "The password reset link is invalid or has expired"
            ),
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def logout(request):
    """
    Log out a user by deleting their token.
    """

    # Log incoming logout request
    logger.info(
        f"Logout request: {request.method} {request.path}",
        extra={"operation": "LOGOUT_REQUEST"},
    )

    user = request.user
    Token.objects.filter(user=user).delete()

    # Log successful logout
    logger.info(
        f"User {user.email} logged out successfully",
        extra={
            "operation": "USER_LOGOUT",
            "user_id": user.id,
            "user_email": user.email,
        },
    )

    return StandardSuccessResponse.create_success_response(
        code=StandardSuccessResponse.LOGOUT_SUCCESS,
        message="Logout successful",
        details="You have been successfully logged out",
        actions="Your session has been terminated securely",
    )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def get_user_name(request):
    """
    Get the authenticated user's name.
    """
    user = request.user
    return StandardSuccessResponse.data_retrieved(
        message="User name retrieved successfully",
        details="Current user's display name",
        data={"name": user.name},
    )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def is_connected_wallet(request):
    """
    Check if the user is connected to a wallet.
    """
    user = request.user
    return StandardSuccessResponse.data_retrieved(
        message="Wallet connection status retrieved successfully",
        details="Current wallet connection information",
        data={
            "is_connected": user.wallet_address is not None,
            "wallet_address": user.wallet_address,
        },
    )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def connect_account(request):
    """
    Connect the user to a wallet.
    """
    user = request.user
    account_address = request.data.get("account_address")
    if not account_address:
        raise_validation_error(
            message="Wallet address is required",
            details="Account address must be provided to connect wallet",
        )
    try:
        user.account_address = account_address
        user.save()
    except Exception as e:
        return handle_exception_with_logging(e, "wallet connection")
    return StandardSuccessResponse.create_success_response(
        code=StandardSuccessResponse.RECORD_UPDATED,
        message="Wallet connected successfully",
        details=f"Account address {account_address} has been linked to your profile",
        actions="You can now perform blockchain transactions",
    )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def opt_in_account(request):
    """
    Opt-in to a smart contract.
    """
    user = request.user
    try:
        user.opt_in = True
        user.save()
    except Exception as e:
        return handle_exception_with_logging(e, "opt-in process")
    return StandardSuccessResponse.create_success_response(
        code=StandardSuccessResponse.RECORD_UPDATED,
        message="Opt-in successful",
        details="You have successfully opted into the smart contract",
        actions="You can now participate in all contract features",
    )


@api_view(["POST"])
@permission_classes([AllowAny])
def refresh_token(request):
    """
    Refresh JWT access token using refresh token
    """

    try:
        from oauth2_auth.jwt_rotation_service import jwt_rotation_service
        from oauth2_auth.rate_limiting_service import rate_limiting_service
        from oauth2_auth.utils import get_client_ip

        # Log incoming token refresh request
        logger.info(
            f"Token refresh request: {request.method} {request.path}",
            extra={"operation": "TOKEN_REFRESH_REQUEST"},
        )

        # Get refresh token from request
        refresh_token = request.data.get("refresh_token")
        if not refresh_token:
            raise_validation_error(
                message="Refresh token is required",
                details="Please provide a valid refresh token",
                error_code="MISSING_REFRESH_TOKEN",
            )

        # Check rate limiting for token refresh
        client_ip = get_client_ip(request)
        rate_check_id = f"{client_ip}_token_refresh"
        is_allowed, rate_info = rate_limiting_service.check_rate_limit(
            rate_check_id, "token_refresh", request
        )
        if not is_allowed:
            raise_validation_error(
                message="Too many token refresh attempts",
                details=rate_info.get("message", "Please try again later"),
                error_code="RATE_LIMITED",
            )

        # Refresh the token
        new_tokens = jwt_rotation_service.refresh_access_token(refresh_token, request)

        if "error" in new_tokens:
            raise_validation_error(
                message="Token refresh failed",
                details=new_tokens["error"],
                error_code="REFRESH_FAILED",
            )

        # Log successful token refresh
        logger.info(
            "Token refreshed successfully",
            extra={
                "operation": "TOKEN_REFRESH_SUCCESS",
                "token_type": new_tokens["token_type"],
                "expires_in": new_tokens["expires_in"],
            },
        )

        return StandardSuccessResponse.create_success_response(
            code=StandardSuccessResponse.TOKEN_REFRESHED,
            message="Token refreshed successfully",
            details="New access token generated",
            actions="Use the new access token for API requests",
            data={
                "access_token": new_tokens["access_token"],
                "refresh_token": new_tokens["refresh_token"],
                "token_type": new_tokens["token_type"],
                "expires_in": new_tokens["expires_in"],
                "refresh_expires_in": new_tokens.get("refresh_expires_in"),
                "scope": new_tokens.get("scope"),
            },
        )

    except (ValidationException, AuthenticationException) as e:
        # Log authentication exceptions
        logger.warning(
            "Token refresh failed due to authentication error",
            extra={"operation": "TOKEN_REFRESH_ERROR", "error_type": type(e).__name__},
        )
        raise
    except Exception as e:
        # Log unexpected token refresh error
        logger.error(
            f"Unexpected token refresh error: {str(e)}",
            extra={"operation": "TOKEN_REFRESH_ERROR", "error": str(e)},
            exc_info=True,
        )
        return handle_exception_with_logging(e, "token refresh")


@api_view(["POST"])
@permission_classes([AllowAny])
def check_password_strength(request):
    """
    Check password strength and provide feedback for fintech-grade requirements

    This endpoint allows frontend applications to validate password strength
    in real-time without submitting the actual registration form.
    """
    try:
        password = request.data.get("password")
        user_info = request.data.get("user_info", {})

        if not password:
            raise_validation_error(
                message="Password is required",
                details="Password field cannot be empty",
            )

        # Get password strength score and feedback
        strength_data = get_password_strength_score(password)

        # Create a temporary user object for similarity checking if user info provided
        temp_user = None
        if user_info:

            class TempUser:
                def __init__(self, info):
                    self.email = info.get("email", "")
                    self.name = info.get("name", "")
                    # Split name into first/last if provided
                    name_parts = self.name.split() if self.name else []
                    self.first_name = name_parts[0] if name_parts else ""
                    self.last_name = (
                        " ".join(name_parts[1:]) if len(name_parts) > 1 else ""
                    )

            temp_user = TempUser(user_info)

        # Test against our fintech validator for detailed feedback
        from .password_validators import FintechPasswordValidator

        validator = FintechPasswordValidator()

        validation_errors = []
        try:
            validator.validate(password, temp_user)
        except ValidationError as e:
            validation_errors = (
                [str(error) for error in e.error_list]
                if hasattr(e, "error_list")
                else [str(e)]
            )

        # Prepare response
        response_data = {
            "strength_score": strength_data["score"],
            "strength_level": strength_data["strength"],
            "meets_requirements": strength_data["meets_requirements"],
            "feedback": strength_data["feedback"],
            "validation_errors": validation_errors,
            "is_valid": len(validation_errors) == 0,
            "requirements": {
                "min_length": 12,
                "requires_uppercase": True,
                "requires_lowercase": True,
                "requires_digit": True,
                "requires_special": True,
                "no_common_passwords": True,
                "no_user_similarity": True,
            },
        }

        logger.info(
            "Password strength check completed",
            extra={
                "event_type": "PASSWORD_STRENGTH_CHECK",
                "strength_score": strength_data["score"],
                "strength_level": strength_data["strength"],
                "is_valid": response_data["is_valid"],
                "password_length": len(password),
            },
        )

        return Response(response_data, status=200)

    except ValidationException:
        # Re-raise validation exceptions
        raise
    except Exception as e:
        logger.error(
            f"Password strength check error: {str(e)}",
            extra={"operation": "PASSWORD_STRENGTH_CHECK_ERROR", "error": str(e)},
            exc_info=True,
        )
        return handle_exception_with_logging(e, "password strength check")
